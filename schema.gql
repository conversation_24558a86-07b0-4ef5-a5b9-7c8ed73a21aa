# ENUMS

enum CaseStatus {
  OPEN
  COLD
  CLOSED
}

enum AgentRole {
  USER
  ADMIN
  DEVELOPER
}

enum VehicleType {
  CAR
  BOAT
  AIRCRAFT
  BIKE
}

enum OperationType {
  APPREHENSION
  SURVEILLANCE
  UNDERCOVER
  SPECIAL
}

# NODE TYPES

type Organization
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in: 

      ["USER", "ADMIN", "DEVELOPER"] 

  } } } } ] ) {

  hiveId:         ID!
  name:           String!
  foundingDate:   Date

  parentOrganization: [Organization!]!  @relationship(type: "PARENT_OF", direction: IN)
  childOrganizations: [Organization!]!  @relationship(type: "PARENT_OF", direction: OUT)

  members:        [Person!]!            @relationship(type: "MEMBER_OF",  direction: IN)
  ownsVehicle:    [Vehicle!]!           @relationship(type: "OWNED_BY",   direction: IN)
  
}

type Person
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in: 

      ["USER", "ADMIN", "DEVELOPER"] 

  } } } } ] ) {

  hiveId:           ID!
  firstName:        String!
  lastName:         String!
  dateOfBirth:      Date!

  memberOf:         [Organization!]!      @relationship(type: "MEMBER_OF",  direction: OUT)
  suspectIn:        [Case!]!              @relationship(type: "SUSPECT_IN", direction: OUT)
  victimIn:         [Case!]!              @relationship(type: "VICTIM_IN",  direction: OUT)
  witnessIn:        [Case!]!              @relationship(type: "WITNESS_IN", direction: OUT)

  ownsVehicle:      [Vehicle!]!           @relationship(type: "OWNED_BY",   direction: IN)

  hasAgent:         [Agent!]!             @relationship(type: "BACKED_BY",  direction: IN)
  hasInformant:     [Informant!]!         @relationship(type: "BACKED_BY",  direction: IN)

}

type Case
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in: 
      
      ["USER", "ADMIN", "DEVELOPER"] 

  } } } } ] ) {

  hiveId:           ID!
  title:            String!
  creationDate:     Date!
  status:           CaseStatus!           @default(value: OPEN)   # "OPEN", "COLD", or "CLOSED"

  suspects:         [Person!]!            @relationship(type: "SUSPECT_IN", direction: IN)
  victims:          [Person!]!            @relationship(type: "VICTIM_IN",  direction: IN)
  witnesses:        [Person!]!            @relationship(type: "WITNESS_IN", direction: IN)

}

type Agent
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in: 
      
      ["ADMIN", "DEVELOPER"] 
      
  } } } } ] ) {
    
  hiveId:           ID!
  username:         String!               
  password:         String!               
  role:             AgentRole!               # "USER" or "ADMIN" or "DEVELOPER"
  backingPerson:    [Person!]!               @relationship(type: "BACKED_BY", direction: OUT)
  handlesInformant: [Informant!]!            @relationship(type: "HANDLED_BY", direction: IN)

}

type Informant
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in:

      ["USER", "ADMIN", "DEVELOPER"]

  } } } } ] ) {

  hiveId:           ID!
  codeName:         String!
  backingPerson:    [Person!]!               @relationship(type: "BACKED_BY", direction: OUT)
  handlerAgent:     [Agent!]!                @relationship(type: "HANDLED_BY", direction: OUT)

}

type Operation
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in:

      ["USER", "ADMIN", "DEVELOPER"]

  } } } } ] ) {

  hiveId:           ID!
  title:            String!
  type:             OperationType!           # "APPREHENSION", "SURVEILLANCE", "UNDERCOVER", "SPECIAL"
  creationDate:     Date!

  targets:          [OperationTarget!]!      @relationship(type: "TARGET_OF", direction: IN)
  leadAgent:        [Agent!]!                @relationship(type: "LEADS", direction: IN)
  scopedToCase:     [Case!]!                 @relationship(type: "SCOPED_TO", direction: OUT)

}

union VehicleOwner = Person | Organization

union OperationTarget = Person | Organization | Vehicle

type Vehicle
  @node @authentication @authorization (
    filter: [{ where: { jwt: { role: { in:

      ["USER", "ADMIN", "DEVELOPER"]

  } } } } ] ) {

  hiveId:           ID!
  type:             VehicleType!             # "CAR", "BOAT", "AIRCRAFT", "BIKE"
  make:             String!
  model:            String!
  color:            String!

  owner:            [VehicleOwner!]!         @relationship(type: "OWNED_BY", direction: OUT)

}

# SPECIAL TYPES

type JWT @jwt {
  role: String!
}

type DeleteResponse {
  hiveId: ID!
  deleted: Boolean!
}

type AuthPayload {
  token: String!
  agent: Agent!
}

# MUTATIONS

type Mutation {

## Create mutations
  createOrganization            (name: String!, foundingDate: Date): Organization!
  createPerson                  (firstName: String!, lastName: String!, dateOfBirth: Date!): Person!
  createCase                    (title: String!): Case!
  createAgent                   (username: String!, password: String!, role: AgentRole!, backingPersonHiveId: ID!): Agent!
  createInformant               (codeName: String!, backingPersonHiveId: ID!): Informant!
  createVehicle                 (type: VehicleType!, make: String!, model: String!, color: String!): Vehicle!
  createOperation               (title: String!, type: OperationType!): Operation!
  
## Update mutations
  updateOrganization            (hiveId: ID!, name: String!, foundingDate: Date): Organization!
  updatePerson                  (hiveId: ID!, firstName: String!, lastName: String!, dateOfBirth: Date!): Person!
  updateCase                    (hiveId: ID!, title: String!, status: CaseStatus): Case!
  updateAgent                   (hiveId: ID!, username: String, password: String, role: AgentRole): Agent!
  updateInformant               (hiveId: ID!, codeName: String): Informant!
  updateVehicleColor            (hiveId: ID!, color: String!): Vehicle!
  updateOperation               (hiveId: ID!, title: String, type: OperationType): Operation!
  
## Delete mutations
  deleteOrganization            (hiveId: ID!): DeleteResponse!
  deletePerson                  (hiveId: ID!): DeleteResponse!
  deleteCase                    (hiveId: ID!): DeleteResponse!
  deleteAgent                   (hiveId: ID!): DeleteResponse!
  deleteInformant               (hiveId: ID!): DeleteResponse!
  deleteVehicle                 (hiveId: ID!): DeleteResponse!
  deleteOperation               (hiveId: ID!): DeleteResponse!
  
## Relationship mutations

  # Organization
  addMemberToOrganization       (personHiveId: ID!, organizationHiveId: ID!): Person!
  removeMemberFromOrganization  (personHiveId: ID!, organizationHiveId: ID!): Person!
  setOrganizationParent         (childOrganizationHiveId: ID!, parentOrganizationHiveId: ID!): Organization!

  # Case
  addSuspectToCase              (personHiveId: ID!, caseHiveId: ID!): Person!
  removeSuspectFromCase         (personHiveId: ID!, caseHiveId: ID!): Person!
  addVictimToCase               (personHiveId: ID!, caseHiveId: ID!): Person!
  removeVictimFromCase          (personHiveId: ID!, caseHiveId: ID!): Person!
  addWitnessToCase              (personHiveId: ID!, caseHiveId: ID!): Person!
  removeWitnessFromCase         (personHiveId: ID!, caseHiveId: ID!): Person!

  # Vehicle
  setVehicleOwner               (vehicleHiveId: ID!, ownerHiveId: ID!): Vehicle!

  # Informant-Agent
  setInformantHandler           (informantHiveId: ID!, agentHiveId: ID!): Informant!

  # Operation
  addTargetToOperation          (targetHiveId: ID!, operationHiveId: ID!): Operation!
  removeTargetFromOperation     (targetHiveId: ID!, operationHiveId: ID!): Operation!
  setLeadAgentForOperation      (agentHiveId: ID!, operationHiveId: ID!): Operation!
  removeLeadAgentFromOperation  (operationHiveId: ID!): Operation!
  setOperationCaseScope         (operationHiveId: ID!, caseHiveId: ID!): Operation!

## Special mutations
  login                         (username: String!, password: String!): AuthPayload!
}
