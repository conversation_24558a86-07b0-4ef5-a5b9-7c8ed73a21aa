const jwt = require('jsonwebtoken');
const { validateDateOfBirth, validateBackingPersonUniqueness, validateNonEmptyString, validateParentOrganization, validateOperationExists, validateAgentExists, validateCaseExists, validateTargetExists, validateTargetRelationshipExists } = require('./validations');
const { GraphQLError } = require('graphql');

// Get JWT secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('[ERROR] JWT_SECRET environment variable is not set');
  process.exit(1);
}

const resolvers = {
  VehicleOwner: {
    __resolveType(obj, context, info){
      if(obj.firstName || obj.lastName || obj.dateOfBirth){
        return 'Person';
      }
      if(obj.name || obj.foundingDate){
        return 'Organization';
      }
      return null;
    },
  },
  OperationTarget: {
    __resolveType(obj, context, info){
      if(obj.firstName || obj.lastName || obj.dateOfBirth){
        return 'Person';
      }
      if(obj.name || obj.foundingDate){
        return 'Organization';
      }
      if(obj.type || obj.make || obj.model || obj.color){
        return 'Vehicle';
      }
      return null;
    },
  },
  Mutation: {

  // Create

    async createOrganization(
      _parent, { name, foundingDate }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (o:Organization { hiveId: newValue, name: $name, foundingDate: $foundingDate })
            RETURN o { .hiveId, .name, .foundingDate }
          `,
          { name, foundingDate }
        );
        return records[0].get('o');
      } finally {
        await session.close();
      }
    },

    async createPerson(
      _parent, { firstName, lastName, dateOfBirth }, { driver }) {

      // --- Validation ---
      validateDateOfBirth(dateOfBirth);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (p:Person { hiveId: newValue, firstName: $firstName, lastName: $lastName, dateOfBirth: $dateOfBirth })
            RETURN p { .hiveId, .firstName, .lastName, .dateOfBirth }
          `,
          { firstName, lastName, dateOfBirth }
        );
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async createCase(
      _parent, { title }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (c:Case { hiveId: newValue, title: $title, creationDate: date(), status: "OPEN" })
            RETURN c { .hiveId, .title, .creationDate, .status }
          `,
          { title }
        );
        const caseObj = records[0].get('c');
        return caseObj;
      } finally {
        await session.close();
      }
    },

    async createAgent(
      _parent, { username, password, role, backingPersonHiveId }, { driver }) {

      // --- Validation ---
      await validateBackingPersonUniqueness(backingPersonHiveId, driver, 'Agent');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            MATCH (p:Person {hiveId: $backingPersonHiveId})
            CREATE (a:Agent { hiveId: newValue, username: $username, password: $password, role: $role })
            CREATE (a)-[:BACKED_BY]->(p)
            RETURN a { 
              .hiveId,
              .username, 
              .role,
              backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { username, password, role, backingPersonHiveId: parseInt(backingPersonHiveId) }
        );
        if (records.length === 0) {
          throw new GraphQLError('Could not create agent or find backing person.', {
            extensions: { code: 'AGENT_CREATION_FAILED' },
          });
        }
        return records[0].get('a');
      } finally {
        await session.close();
      }
    },

    async createInformant(
      _parent, { codeName, backingPersonHiveId }, { driver }) {

      // --- Validation ---
      await validateBackingPersonUniqueness(backingPersonHiveId, driver, 'Informant');
      validateNonEmptyString(codeName, 'codeName');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            MATCH (p:Person {hiveId: $backingPersonHiveId})
            CREATE (i:Informant { hiveId: newValue, codeName: $codeName })
            CREATE (i)-[:BACKED_BY]->(p)
            RETURN i { 
              .hiveId,
              .codeName,
              backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { codeName, backingPersonHiveId: parseInt(backingPersonHiveId) }
        );
        if (records.length === 0) {
          throw new GraphQLError('Could not create informant or find backing person.', {
            extensions: { code: 'INFORMANT_CREATION_FAILED' },
          });
        }
        const informant = records[0].get('i');
        return informant;
      } finally {
        await session.close();
      }
    },

    async createVehicle(
      _parent, { type, make, model, color }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(type, 'type');
      validateNonEmptyString(make, 'make');
      validateNonEmptyString(model, 'model');
      validateNonEmptyString(color, 'color');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (v:Vehicle { hiveId: newValue, type: $type, make: $make, model: $model, color: $color })
            RETURN v { .hiveId, .type, .make, .model, .color }
          `,
          { type, make, model, color }
        );
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async createOperation(
      _parent, { title, type }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(title, 'title');
      validateNonEmptyString(type, 'type');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MERGE (ctr:IdCounter { name: 'GlobalHiveID' })
            ON CREATE SET ctr.value = 0
            WITH ctr
            CALL apoc.atomic.add(ctr, 'value', 1) YIELD newValue
            CREATE (op:Operation { hiveId: newValue, title: $title, type: $type, creationDate: date() })
            RETURN op { .hiveId, .title, .type, .creationDate }
          `,
          { title, type }
        );
        return records[0].get('op');
      } finally {
        await session.close();
      }
    },


  // Delete

    async deleteOrganization(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (o:Organization {hiveId: $hiveId})
            WITH o, o.hiveId AS idToDelete
            DETACH DELETE o
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        if (records.length === 0) {
          throw new Error(`Organization with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deletePerson(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $hiveId})
            WITH p, p.hiveId AS idToDelete
            DETACH DELETE p
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        if (records.length === 0) {
          throw new Error(`Person with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteCase(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (c:Case {hiveId: $hiveId})
            WITH c, c.hiveId AS idToDelete
            DETACH DELETE c
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        if (records.length === 0) {
          throw new Error(`Case with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteAgent(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent {hiveId: $hiveId})
            WITH a, a.hiveId AS idToDelete
            DETACH DELETE a
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        
        if (records.length === 0) {
          throw new Error(`Agent with hiveId ${hiveId} not found`);
        }
        
        return {
          hiveId: records[0].get('deletedHiveId').toString(),
          deleted: true
        };
      } finally {
        await session.close();
      }
    },

    async deleteInformant(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (i:Informant {hiveId: $hiveId})
            WITH i, i.hiveId AS idToDelete
            DETACH DELETE i
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        if (records.length === 0) {
          throw new GraphQLError(`Informant with hiveId ${hiveId} not found`, {
            extensions: { code: 'NOT_FOUND', argumentName: 'hiveId', providedValue: hiveId },
          });
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteVehicle(
      _parent, { hiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $hiveId})
            WITH v, v.hiveId AS idToDelete
            DETACH DELETE v
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        if (records.length === 0) {
          throw new Error(`Vehicle with hiveId ${hiveId} not found`);
        }
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

    async deleteOperation(
      _parent, { hiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, hiveId, 'delete operation');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $hiveId})
            WITH op, op.hiveId AS idToDelete
            DETACH DELETE op
            RETURN idToDelete as deletedHiveId
          `,
          { hiveId: parseInt(hiveId) }
        );
        return { hiveId: records[0].get('deletedHiveId').toString(), deleted: true };
      } finally {
        await session.close();
      }
    },

  // Update

    async updateOrganization(
      _parent, { hiveId, name, foundingDate }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (o:Organization {hiveId: $hiveId})
            SET o.name = $name, o.foundingDate = $foundingDate
            RETURN o { .hiveId, .name, .foundingDate }
          `,
          { hiveId: parseInt(hiveId), name, foundingDate }
        );
        
        if (records.length === 0) {
          throw new Error(`Organization with hiveId ${hiveId} not found`);
        }
        
        return records[0].get('o');
      } finally {
        await session.close();
      }
    },

    async updatePerson(
      _parent, { hiveId, firstName, lastName, dateOfBirth }, { driver }) {

      // --- Validation ---
      validateDateOfBirth(dateOfBirth);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $hiveId})
            SET p.firstName = $firstName, p.lastName = $lastName, p.dateOfBirth = $dateOfBirth
            RETURN p { .hiveId, .firstName, .lastName, .dateOfBirth }
          `,
          { hiveId: parseInt(hiveId), firstName, lastName, dateOfBirth }
        );
        
        if (records.length === 0) {
          throw new Error(`Person with hiveId ${hiveId} not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async updateCase(
      _parent, { hiveId, title, status }, { driver }) {

      // --- Validation ---
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (c:Case {hiveId: $hiveId})
            SET c.title = $title, c.status = $status
            RETURN c { .hiveId, .title, .creationDate, .status }
          `,
          { hiveId: parseInt(hiveId), title, status }
        );
        if (records.length === 0) {
          throw new Error(`Case with hiveId ${hiveId} not found`);
        }
        return records[0].get('c');
      } finally {
        await session.close();
      }
    },

    async updateAgent(
      _parent, { hiveId, username, password, role }, { driver }) {

      // --- Validation ---
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent {hiveId: $hiveId})
            SET a.username = COALESCE($username, a.username),
                a.password = COALESCE($password, a.password),
                a.role = COALESCE($role, a.role)
            RETURN a { 
              .username, 
              .role,
              hiveId: toString(a.hiveId)
            }
          `,
          { hiveId: parseInt(hiveId), username, password, role }
        );
        
        if (records.length === 0) {
          throw new Error(`Agent with hiveId ${hiveId} not found`);
        }
        
        return records[0].get('a');
      } finally {
        await session.close();
      }
    },

    async updateInformant(
      _parent, { hiveId, codeName }, { driver }) {
      
      // --- Validation ---
      if (codeName !== undefined) {
        validateNonEmptyString(codeName, 'codeName');
      }
      // --- End Validation ---

      const session = driver.session();
      try {
        const result = await session.run(
          `
            MATCH (i:Informant {hiveId: $hiveId})
            SET i.codeName = coalesce($codeName, i.codeName)
            WITH i
            MATCH (i)-[:BACKED_BY]->(p:Person)
            RETURN i { 
                .hiveId, 
                .codeName, 
                backingPerson: [p { .hiveId, .firstName, .lastName }]
            }
          `,
          { hiveId: parseInt(hiveId), codeName }
        );
 
        if (result.records.length === 0) {
          throw new GraphQLError(`Informant with hiveId ${hiveId} not found.`, {
            extensions: { code: 'NOT_FOUND', argumentName: 'hiveId', providedValue: hiveId },
          });
        }
        const informant = result.records[0].get('i');
        return informant;
      } finally {
        await session.close();
      }
    },

    async updateVehicleColor(
      _parent, { hiveId, color }, { driver }) {

      // --- Validation ---
      validateNonEmptyString(color, 'color');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $hiveId})
            SET v.color = $color
            RETURN v { .hiveId, .type, .make, .model, .color }
          `,
          { hiveId: parseInt(hiveId), color }
        );
        if (records.length === 0) {
          throw new Error(`Vehicle with hiveId ${hiveId} not found`);
        }
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async updateOperation(
      _parent, { hiveId, title, type }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, hiveId, 'update operation');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $hiveId})
            SET op.title = COALESCE($title, op.title),
                op.type = COALESCE($type, op.type)
            RETURN op { .hiveId, .title, .type, .creationDate }
          `,
          { hiveId: parseInt(hiveId), title, type }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

  // Relationship mutations

    async addMemberToOrganization(
      _parent, { personHiveId, organizationHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (o:Organization {hiveId: $organizationHiveId})
            MERGE (p)-[:MEMBER_OF]->(o)
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              .dateOfBirth,
              memberOf: [(p)-[:MEMBER_OF]->(org) | org {.hiveId, .name, .foundingDate}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            organizationHiveId: parseInt(organizationHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Organization not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeMemberFromOrganization(
      _parent, { personHiveId, organizationHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (o:Organization {hiveId: $organizationHiveId})
            MATCH (p)-[r:MEMBER_OF]->(o)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              .dateOfBirth,
              memberOf: [(p)-[:MEMBER_OF]->(org) | org {.hiveId, .name, .foundingDate}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            organizationHiveId: parseInt(organizationHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Organization not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addSuspectToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[:SUSPECT_IN]->(c)
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              suspectIn: [(p)-[:SUSPECT_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeSuspectFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:SUSPECT_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              suspectIn: [(p)-[:SUSPECT_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addVictimToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[:VICTIM_IN]->(c)
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              victimIn: [(p)-[:VICTIM_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeVictimFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:VICTIM_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              victimIn: [(p)-[:VICTIM_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async addWitnessToCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MERGE (p)-[:WITNESS_IN]->(c)
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              witnessIn: [(p)-[:WITNESS_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },
    
    async removeWitnessFromCase(
      _parent, { personHiveId, caseHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (p:Person {hiveId: $personHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            MATCH (p)-[r:WITNESS_IN]->(c)
            DELETE r
            RETURN p { 
              .hiveId, 
              .firstName,
              .lastName,
              witnessIn: [(p)-[:WITNESS_IN]->(c) | c {.hiveId, .title}] 
            }
          `,
          { 
            personHiveId: parseInt(personHiveId), 
            caseHiveId: parseInt(caseHiveId) 
          }
        );
        
        if (records.length === 0) {
          throw new Error(`Person or Case not found, or no relationship exists`);
        }
        
        return records[0].get('p');
      } finally {
        await session.close();
      }
    },

    async setVehicleOwner(
      _parent, { vehicleHiveId, ownerHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (v:Vehicle {hiveId: $vehicleHiveId})
            MATCH (newOwner) WHERE newOwner.hiveId = $ownerHiveId AND (newOwner:Person OR newOwner:Organization)
            // If both vehicle and new owner are found, proceed
            WITH v, newOwner
            OPTIONAL MATCH (v)-[oldRel:OWNED_BY]->()
            DELETE oldRel
            CREATE (v)-[newRel:OWNED_BY]->(newOwner)
            RETURN v {
              .hiveId,
              .type,
              .make,
              .model,
              .color,
              owner: [newOwner {
                __typename: CASE WHEN newOwner:Person THEN 'Person' ELSE 'Organization' END,
                .hiveId,
                firstName: newOwner.firstName,
                lastName: newOwner.lastName,
                dateOfBirth: newOwner.dateOfBirth,
                name: newOwner.name,
                foundingDate: newOwner.foundingDate
              }]
            }
          `,
          { vehicleHiveId: parseInt(vehicleHiveId), ownerHiveId: parseInt(ownerHiveId) }
        );

        if (records.length === 0) {
          throw new GraphQLError(`Vehicle with hiveId ${vehicleHiveId} or Owner with hiveId ${ownerHiveId} not found, or operation failed.`, {
            extensions: { code: 'SET_OWNER_FAILED' },
          });
        }
        return records[0].get('v');
      } finally {
        await session.close();
      }
    },

    async setInformantHandler(
      _parent, { informantHiveId, agentHiveId }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (i:Informant {hiveId: $informantHiveId})
            MATCH (a:Agent {hiveId: $agentHiveId}) // Try to find the new agent first
            // If both informant and new agent are found, proceed
            WITH i, a
            OPTIONAL MATCH (i)-[oldRel:HANDLED_BY]->() // Find any existing handler
            DELETE oldRel // Delete it
            CREATE (i)-[newRel:HANDLED_BY]->(a) // Create the new relationship
            RETURN i {
              .hiveId,
              .codeName,
              backingPerson: [(i)-[:BACKED_BY]->(bp:Person) | bp {.hiveId, .firstName, .lastName}],
              handlerAgent: [a {
                .hiveId,
                .username,
                .role
              }]
            }
          `,
          { informantHiveId: parseInt(informantHiveId), agentHiveId: parseInt(agentHiveId) }
        );

        if (records.length === 0) {
          throw new GraphQLError('Could not set informant handler. Informant or Agent not found.', {
            extensions: { code: 'SET_HANDLER_FAILED' },
          });
        }
        return records[0].get('i');
      } finally {
        await session.close();
      }
    },

    async setOrganizationParent(
      _parent, { childOrganizationHiveId, parentOrganizationHiveId }, { driver }) {

      // --- Validation ---
      await validateParentOrganization(driver, childOrganizationHiveId, parentOrganizationHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        // Remove any existing parent relationship for the child organization
        await session.run(
          `
            MATCH (:Organization)-[r:PARENT_OF]->(child:Organization {hiveId: $childOrganizationHiveId})
            DELETE r
          `,
          { childOrganizationHiveId: parseInt(childOrganizationHiveId) }
        );

        // Create the new parent relationship
        const { records } = await session.run(
          `
            MATCH (child:Organization {hiveId: $childOrganizationHiveId})
            MATCH (parent:Organization {hiveId: $parentOrganizationHiveId})
            MERGE (parent)-[:PARENT_OF]->(child)
            RETURN child {
              .hiveId,
              .name,
              parentOrganization: [(orgParent)-[:PARENT_OF]->(child) | orgParent { .name, .hiveId }],
              childOrganizations: [(child)-[:PARENT_OF]->(orgChild) | orgChild { .name, .hiveId }]
            }
          `,
          {
            childOrganizationHiveId: parseInt(childOrganizationHiveId),
            parentOrganizationHiveId: parseInt(parentOrganizationHiveId)
          }
        );
        if (records.length === 0) {
          throw new GraphQLError('Child or Parent Organization not found.', {
            extensions: { code: 'ORGANIZATION_NOT_FOUND' },
          });
        }
        return records[0].get('child');
      } finally {
        await session.close();
      }
    },

    async addTargetToOperation(
      _parent, { targetHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'adding target');
      await validateTargetExists(driver, targetHiveId, 'adding target');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
            MERGE (target)-[:TARGET_OF]->(op)
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              targets: [(t)-[:TARGET_OF]->(op) | t {
                __typename: CASE
                  WHEN t:Person THEN 'Person'
                  WHEN t:Organization THEN 'Organization'
                  ELSE 'Vehicle'
                END,
                .hiveId,
                firstName: t.firstName,
                lastName: t.lastName,
                dateOfBirth: t.dateOfBirth,
                name: t.name,
                foundingDate: t.foundingDate,
                type: t.type,
                make: t.make,
                model: t.model,
                color: t.color
              }]
            }
          `,
          {
            targetHiveId: parseInt(targetHiveId),
            operationHiveId: parseInt(operationHiveId)
          }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async removeTargetFromOperation(
      _parent, { targetHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'removing target');
      await validateTargetExists(driver, targetHiveId, 'removing target');
      await validateTargetRelationshipExists(driver, targetHiveId, operationHiveId);
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
            MATCH (target)-[r:TARGET_OF]->(op)
            DELETE r
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              targets: [(t)-[:TARGET_OF]->(op) | t {
                __typename: CASE
                  WHEN t:Person THEN 'Person'
                  WHEN t:Organization THEN 'Organization'
                  ELSE 'Vehicle'
                END,
                .hiveId,
                firstName: t.firstName,
                lastName: t.lastName,
                dateOfBirth: t.dateOfBirth,
                name: t.name,
                foundingDate: t.foundingDate,
                type: t.type,
                make: t.make,
                model: t.model,
                color: t.color
              }]
            }
          `,
          {
            targetHiveId: parseInt(targetHiveId),
            operationHiveId: parseInt(operationHiveId)
          }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async setLeadAgentForOperation(
      _parent, { agentHiveId, operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'setting lead agent');
      await validateAgentExists(driver, agentHiveId, 'setting as lead agent');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (a:Agent {hiveId: $agentHiveId})
            // If both operation and agent are found, proceed
            WITH op, a
            OPTIONAL MATCH (oldAgent:Agent)-[oldRel:LEADS]->(op)
            DELETE oldRel
            CREATE (a)-[newRel:LEADS]->(op)
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              leadAgent: [a {
                .hiveId,
                .username,
                .role
              }]
            }
          `,
          { agentHiveId: parseInt(agentHiveId), operationHiveId: parseInt(operationHiveId) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async removeLeadAgentFromOperation(
      _parent, { operationHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'removing lead agent');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            OPTIONAL MATCH (a:Agent)-[r:LEADS]->(op)
            DELETE r
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              leadAgent: []
            }
          `,
          { operationHiveId: parseInt(operationHiveId) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },

    async setOperationCaseScope(
      _parent, { operationHiveId, caseHiveId }, { driver }) {

      // --- Validation ---
      await validateOperationExists(driver, operationHiveId, 'setting case scope');
      await validateCaseExists(driver, caseHiveId, 'setting operation scope');
      // --- End Validation ---

      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (op:Operation {hiveId: $operationHiveId})
            MATCH (c:Case {hiveId: $caseHiveId})
            // If both operation and case are found, proceed
            WITH op, c
            OPTIONAL MATCH (op)-[oldRel:SCOPED_TO]->()
            DELETE oldRel
            CREATE (op)-[newRel:SCOPED_TO]->(c)
            RETURN op {
              .hiveId,
              .title,
              .type,
              .creationDate,
              scopedToCase: [c {
                .hiveId,
                .title,
                .creationDate,
                .status
              }]
            }
          `,
          { operationHiveId: parseInt(operationHiveId), caseHiveId: parseInt(caseHiveId) }
        );

        return records[0].get('op');
      } finally {
        await session.close();
      }
    },


  // Special mutations

    async login(_parent, { username, password }, { driver }) {
      const session = driver.session();
      try {
        const { records } = await session.run(
          `
            MATCH (a:Agent { username: $username, password: $password })
            RETURN a { 
              .username, 
              .role,
              hiveId: toString(a.hiveId)
            }
          `,
          { username, password }
        );
        if (records.length === 0) {
          throw new Error('Invalid credentials');
        }
        const agent = records[0].get('a');
        const token = jwt.sign(
          { hiveId: agent.hiveId, username: agent.username, role: agent.role }, 
          JWT_SECRET, 
          { expiresIn: '12h' }
        );
        return { token, agent };
      } finally {
        await session.close();
      }
    }
  }
};

// Export resolvers and the secret
module.exports = { resolvers, JWT_SECRET }; 