// validations.js
const { GraphQLError } = require('graphql');

const EARLIEST_DOB = new Date('1900-01-01');

function validateNonEmptyString(value, fieldName) {
// General string validation: not null, not undefined, not empty, not just whitespace

    if (value === null || value === undefined || typeof value !== 'string' || value.trim() === '') {
        throw new GraphQLError(`${fieldName} cannot be empty or just whitespace.`, {
            extensions: { code: 'BAD_USER_INPUT', argumentName: fieldName, providedValue: value },
        });
    }
}

function validateDateOfBirth(dateOfBirthInput) {
// Checks dob format and date range on person create. Skips if dob is not provided.

     if (dateOfBirthInput === null || dateOfBirthInput === undefined) {
        return;
    }

    let dob;

    if (typeof dateOfBirthInput === 'string') {
        dob = new Date(dateOfBirthInput); // Parses "YYYY-MM-DD" as UTC midnight
    } else if (dateOfBirthInput instanceof Date) {
        dob = dateOfBirthInput;
    } else if (typeof dateOfBirthInput === 'object' &&
               dateOfBirthInput !== null && // Ensure it's not 'null' which is also typeof 'object'
               typeof dateOfBirthInput.year === 'number' &&
               typeof dateOfBirthInput.month === 'number' &&
               typeof dateOfBirthInput.day === 'number') {
        // Construct date using UTC to avoid timezone issues from components, don't you fucking dare change this.
        dob = new Date(Date.UTC(dateOfBirthInput.year, dateOfBirthInput.month - 1, dateOfBirthInput.day));
    } else {
        throw new GraphQLError('Date of birth must be provided as a string, a Date object, or an object with year, month, and day properties. e.g "2025-01-01" or { year: 2025, month: 1, day: 1 }', {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'dateOfBirth', providedValue: dateOfBirthInput },
        });
    }
    
    try {
        // Get today's date and set it to UTC midnight for correct comparison
        const todayAtUtcMidnight = new Date();
        todayAtUtcMidnight.setUTCHours(0, 0, 0, 0);

        if (isNaN(dob.getTime())) {
            throw new Error('Invalid date value.'); 
        }
        if (dob > todayAtUtcMidnight) {
            throw new Error('Date of birth cannot be in the future.');
        }
        if (dob < EARLIEST_DOB) {
            throw new Error(`Date of birth cannot be before ${EARLIEST_DOB.getUTCFullYear()}.`);
        }

    } catch (error) {
        throw new GraphQLError(`Invalid dateOfBirth: ${error.message}`, {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'dateOfBirth', providedValue: dateOfBirthInput },
        });
    }
}

async function validateBackingPersonUniqueness(backingPersonHiveId, driver, nodeTypeToCreate) {
    if (backingPersonHiveId === null || backingPersonHiveId === undefined) {
        throw new GraphQLError('Backing person ID must be provided.', {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'backingPersonHiveId' },
        });
    }

    const session = driver.session();
    try {
        const result = await session.run(
            `
            MATCH (p:Person {hiveId: $backingPersonHiveId})<-[r:BACKED_BY]-(existingNode)
            WHERE existingNode:Agent OR existingNode:Informant
            RETURN existingNode
            LIMIT 1
            `,
            { backingPersonHiveId: parseInt(backingPersonHiveId) }
        );

        if (result.records.length > 0) {
            const existingNode = result.records[0].get('existingNode');
            let nodeLabel = 'an agent or informant'; // Default message
            if (existingNode.labels.includes('Agent')) {
                nodeLabel = 'an Agent';
            } else if (existingNode.labels.includes('Informant')) {
                nodeLabel = 'an Informant';
            }
            throw new GraphQLError(`This person (ID: ${backingPersonHiveId}) is already backing ${nodeLabel}. A person can only back one agent or informant.`, {
                extensions: { 
                    code: 'BAD_USER_INPUT', 
                    argumentName: 'backingPersonHiveId', 
                    providedValue: backingPersonHiveId 
                },
            });
        }
    } finally {
        await session.close();
    }
}

async function validateParentOrganization(driver, childOrgId, parentOrgId) {
  if (childOrgId === parentOrgId) {
    throw new GraphQLError('An organization cannot be its own parent.', {
      extensions: { code: 'SELF_PARENTING_NOT_ALLOWED' },
    });
  }

  const session = driver.session();
  try {
    // Check for direct or indirect circular dependencies
    const result = await session.run(
      `
        MATCH (child:Organization {hiveId: $childOrgId})
        MATCH (parent:Organization {hiveId: $parentOrgId})
        MATCH path = (child)-[:PARENT_OF*]->(parent)
        RETURN path
      `,
      { childOrgId: parseInt(childOrgId), parentOrgId: parseInt(parentOrgId) }
    );

    if (result.records.length > 0) {
      throw new GraphQLError(
        'Setting this parent organization would create a circular dependency.',
        {
          extensions: { code: 'CIRCULAR_DEPENDENCY' },
        }
      );
    }
  } finally {
    await session.close();
  }
}

// Entity existence validation functions for Operations

async function validateOperationExists(driver, operationHiveId, operationContext = 'operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (op:Operation {hiveId: $operationHiveId}) RETURN op`,
      { operationHiveId: parseInt(operationHiveId) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`Operation with hiveId ${operationHiveId} not found for ${operationContext}`, {
        extensions: { code: 'OPERATION_NOT_FOUND', argumentName: 'operationHiveId', providedValue: operationHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateAgentExists(driver, agentHiveId, operationContext = 'agent operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (a:Agent {hiveId: $agentHiveId}) RETURN a`,
      { agentHiveId: parseInt(agentHiveId) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`Agent with hiveId ${agentHiveId} not found for ${operationContext}`, {
        extensions: { code: 'AGENT_NOT_FOUND', argumentName: 'agentHiveId', providedValue: agentHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateCaseExists(driver, caseHiveId, operationContext = 'case operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (c:Case {hiveId: $caseHiveId}) RETURN c`,
      { caseHiveId: parseInt(caseHiveId) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`Case with hiveId ${caseHiveId} not found for ${operationContext}`, {
        extensions: { code: 'CASE_NOT_FOUND', argumentName: 'caseHiveId', providedValue: caseHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTargetExists(driver, targetHiveId, operationContext = 'target operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle) RETURN target`,
      { targetHiveId: parseInt(targetHiveId) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] ID ${targetHiveId} is not a valid target. (must be Person, Organization, or Vehicle)`, {
        extensions: { code: 'TARGET_NOT_FOUND', argumentName: 'targetHiveId', providedValue: targetHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTargetRelationshipExists(driver, targetHiveId, operationHiveId) {
  const session = driver.session();
  try {
    const result = await session.run(
      `
        MATCH (op:Operation {hiveId: $operationHiveId})
        MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
        MATCH (target)-[r:TARGET_OF]->(op)
        RETURN r
      `,
      { targetHiveId: parseInt(targetHiveId), operationHiveId: parseInt(operationHiveId) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] Operation ${operationHiveId} is not targeting Hive ID ${targetHiveId}.`, {
        extensions: { code: 'RELATIONSHIP_NOT_FOUND', argumentName: 'targetHiveId', providedValue: targetHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

module.exports = {
    validateDateOfBirth,
    validateBackingPersonUniqueness,
    validateNonEmptyString,
    validateParentOrganization,
    validateOperationExists,
    validateAgentExists,
    validateCaseExists,
    validateTargetExists,
    validateTargetRelationshipExists,
};